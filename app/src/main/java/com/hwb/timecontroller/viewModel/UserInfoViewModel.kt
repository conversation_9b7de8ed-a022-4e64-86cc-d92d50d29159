package com.hwb.timecontroller.viewModel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hwb.timecontroller.business.BalancePollingManager
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.network.ClientLoginData
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 用户信息页面ViewModel
 * 
 * Author: huangwubin
 * Date: 2025/7/11
 */
class UserInfoViewModel(application: Application) : AndroidViewModel(application) {

    // 用户数据
    private val _userData = MutableLiveData<ClientLoginData?>()
    val userData: LiveData<ClientLoginData?> = _userData

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    // 退出登录结果
    private val _logoutResult = MutableLiveData<Boolean>()
    val logoutResult: LiveData<Boolean> = _logoutResult

    // 余额刷新状态
    private val _isRefreshingBalance = MutableLiveData<Boolean>()
    val isRefreshingBalance: LiveData<Boolean> = _isRefreshingBalance

    // 开始游玩结果
    private val _startPlayingResult = MutableLiveData<Boolean>()
    val startPlayingResult: LiveData<Boolean> = _startPlayingResult

    init {
        _isLoading.value = false
        _isRefreshingBalance.value = false
    }

    /**
     * 设置用户数据
     * @param clientData 客户端登录数据
     */
    fun setUserData(clientData: ClientLoginData?) {
        _userData.value = clientData
        XLog.d("设置用户数据: ${clientData?.userName}")
    }

    /**
     * 退出登录
     */
    fun logout() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // 使用UserManager的logout方法清除本地用户信息和登录状态
                UserManager.logout()

                // 清除内存中的用户数据
                _userData.value = null

                _logoutResult.value = true
                XLog.d("用户退出登录成功")

            } catch (e: Exception) {
                _errorMessage.value = "退出登录失败: ${e.message}"
                _logoutResult.value = false
                XLog.e("退出登录失败", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 检查余额是否足够一分钟游玩时间
     * @return 是否足够
     */
    fun checkBalanceForOneMinute(): Boolean {
        return BalancePollingManager.isBalanceSufficientForMinutes(1.0)
    }

    /**
     * 获取当前余额信息（用于显示提示）
     */
    fun getCurrentBalanceInfo(): Pair<Double?, Double?> {
        val currentBalance = BalancePollingManager.getCurrentBalance()
        val perMinuteCost = BalancePollingManager.getPerMinuteCost()
        return Pair(currentBalance, perMinuteCost)
    }

    /**
     * 主动刷新余额（进入页面时调用）
     */
    fun refreshBalanceOnEnter() {
        viewModelScope.launch {
            try {
                _isRefreshingBalance.value = true
                BalancePollingManager.refreshBalance()
                XLog.d("进入用户信息页面，余额刷新完成")
            } catch (e: Exception) {
                XLog.e("进入用户信息页面，余额刷新失败", e)
                _errorMessage.value = "刷新余额失败: ${e.message}"
            } finally {
                _isRefreshingBalance.value = false
            }
        }
    }

    /**
     * 获取用户显示名称
     * @return 用户名称，如果为空则返回默认值
     */
    fun getUserDisplayName(): String {
        val userName = _userData.value?.userName
        return when {
            userName.isNullOrBlank() -> "未知用户"
            userName.length > 20 -> userName.take(20) + "..." // 限制显示长度
            else -> userName
        }
    }

    /**
     * 获取用户余额显示文本
     * @return 格式化的余额文本
     */
    fun getUserBalanceText(): String {
        val money = _userData.value?.money
        return when {
            money == null -> "0"
            money < 0 -> "0" // 防止显示负数
            money > 999999 -> "999999+" // 防止显示过大的数字
            else -> {
                // 如果是整数，显示为整数；否则显示为小数
                if (money == money.toInt().toDouble()) {
                    money.toInt().toString()
                } else {
                    String.format("%.1f", money)
                }
            }
        }
    }

    /**
     * 获取用户头像URL
     * @return 头像URL，可能为null
     */
    fun getUserAvatarUrl(): String? {
        return _userData.value?.headImg
    }

    /**
     * 检查是否有用户数据
     * @return 是否有有效的用户数据
     */
    fun hasUserData(): Boolean {
        return _userData.value != null && !_userData.value?.id.isNullOrEmpty()
    }

    /**
     * 手动刷新用户余额
     */
    fun refreshBalance() {
        viewModelScope.launch {
            try {
                _isRefreshingBalance.value = true

                val result = BalancePollingManager.refreshBalance()
                if (result.isSuccess) {
                    val balanceData = result.getOrNull()
                    if (balanceData != null) {
                        // 更新当前用户数据中的余额信息
                        val currentData = _userData.value
                        if (currentData != null) {
                            val updatedData = currentData.copy(
                                money = balanceData.money,
                                temp_time = balanceData.temp_time
                            )
                            _userData.value = updatedData
                        }
                        XLog.d("手动刷新余额成功: money=${balanceData.money}, temp_time=${balanceData.temp_time}")
                    }
                } else {
                    val error = result.exceptionOrNull()
                    _errorMessage.value = "刷新余额失败: ${error?.message ?: "未知错误"}"
                    XLog.e("手动刷新余额失败", error)
                }

            } catch (e: Exception) {
                _errorMessage.value = "刷新余额失败: ${e.message}"
                XLog.e("手动刷新余额异常", e)
            } finally {
                _isRefreshingBalance.value = false
            }
        }
    }
}
